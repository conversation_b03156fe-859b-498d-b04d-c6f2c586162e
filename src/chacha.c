#include "mercha.h"
#include <immintrin.h>  // AVX2/AVX-512指令集

// AVX-512版本: 8路并行ChaCha20
// 定义AVX-512版本的ChaCha20运算，每次处理8个state同时处理8个block
#define AVX512_ROTL32(x, n) _mm512_or_si512(_mm512_slli_epi32((x), (n)), _mm512_srli_epi32((x), (32-(n))))

static void chacha20_block_avx512(uint32_t state[8][16], uint8_t out[8][64]) {
    // 加载8个state到AVX-512寄存器
    __m512i working_state[16];
    for (int i = 0; i < 16; i++) {
        working_state[i] = _mm512_set_epi32(
            state[7][i], state[6][i], state[5][i], state[4][i],
            state[3][i], state[2][i], state[1][i], state[0][i],
            state[7][i], state[6][i], state[5][i], state[4][i],
            state[3][i], state[2][i], state[1][i], state[0][i]
        );
    }

    // 10轮ChaCha20运算，每轮包含8个quarter round
    for (int round = 0; round < 10; round++) {
        // Column rounds - 8路并行
        // Quarter round (0, 4, 8, 12)
        working_state[0] = _mm512_add_epi32(working_state[0], working_state[4]);
        working_state[12] = AVX512_ROTL32(_mm512_xor_si512(working_state[12], working_state[0]), 16);
        working_state[8] = _mm512_add_epi32(working_state[8], working_state[12]);
        working_state[4] = AVX512_ROTL32(_mm512_xor_si512(working_state[4], working_state[8]), 12);

        working_state[0] = _mm512_add_epi32(working_state[0], working_state[4]);
        working_state[12] = AVX512_ROTL32(_mm512_xor_si512(working_state[12], working_state[0]), 8);
        working_state[8] = _mm512_add_epi32(working_state[8], working_state[12]);
        working_state[4] = AVX512_ROTL32(_mm512_xor_si512(working_state[4], working_state[8]), 7);

        // Quarter round (1, 5, 9, 13)
        working_state[1] = _mm512_add_epi32(working_state[1], working_state[5]);
        working_state[13] = AVX512_ROTL32(_mm512_xor_si512(working_state[13], working_state[1]), 16);
        working_state[9] = _mm512_add_epi32(working_state[9], working_state[13]);
        working_state[5] = AVX512_ROTL32(_mm512_xor_si512(working_state[5], working_state[9]), 12);

        working_state[1] = _mm512_add_epi32(working_state[1], working_state[5]);
        working_state[13] = AVX512_ROTL32(_mm512_xor_si512(working_state[13], working_state[1]), 8);
        working_state[9] = _mm512_add_epi32(working_state[9], working_state[13]);
        working_state[5] = AVX512_ROTL32(_mm512_xor_si512(working_state[5], working_state[9]), 7);

        // Quarter round (2, 6, 10, 14)
        working_state[2] = _mm512_add_epi32(working_state[2], working_state[6]);
        working_state[14] = AVX512_ROTL32(_mm512_xor_si512(working_state[14], working_state[2]), 16);
        working_state[10] = _mm512_add_epi32(working_state[10], working_state[14]);
        working_state[6] = AVX512_ROTL32(_mm512_xor_si512(working_state[6], working_state[10]), 12);

        working_state[2] = _mm512_add_epi32(working_state[2], working_state[6]);
        working_state[14] = AVX512_ROTL32(_mm512_xor_si512(working_state[14], working_state[2]), 8);
        working_state[10] = _mm512_add_epi32(working_state[10], working_state[14]);
        working_state[6] = AVX512_ROTL32(_mm512_xor_si512(working_state[6], working_state[10]), 7);

        // Quarter round (3, 7, 11, 15)
        working_state[3] = _mm512_add_epi32(working_state[3], working_state[7]);
        working_state[15] = AVX512_ROTL32(_mm512_xor_si512(working_state[15], working_state[3]), 16);
        working_state[11] = _mm512_add_epi32(working_state[11], working_state[15]);
        working_state[7] = AVX512_ROTL32(_mm512_xor_si512(working_state[7], working_state[11]), 12);

        working_state[3] = _mm512_add_epi32(working_state[3], working_state[7]);
        working_state[15] = AVX512_ROTL32(_mm512_xor_si512(working_state[15], working_state[3]), 8);
        working_state[11] = _mm512_add_epi32(working_state[11], working_state[15]);
        working_state[7] = AVX512_ROTL32(_mm512_xor_si512(working_state[7], working_state[11]), 7);

        // Diagonal rounds - 8路并行
        // Quarter round (0, 5, 10, 15)
        working_state[0] = _mm512_add_epi32(working_state[0], working_state[5]);
        working_state[15] = AVX512_ROTL32(_mm512_xor_si512(working_state[15], working_state[0]), 16);
        working_state[10] = _mm512_add_epi32(working_state[10], working_state[15]);
        working_state[5] = AVX512_ROTL32(_mm512_xor_si512(working_state[5], working_state[10]), 12);

        working_state[0] = _mm512_add_epi32(working_state[0], working_state[5]);
        working_state[15] = AVX512_ROTL32(_mm512_xor_si512(working_state[15], working_state[0]), 8);
        working_state[10] = _mm512_add_epi32(working_state[10], working_state[15]);
        working_state[5] = AVX512_ROTL32(_mm512_xor_si512(working_state[5], working_state[10]), 7);

        // Quarter round (1, 6, 11, 12)
        working_state[1] = _mm512_add_epi32(working_state[1], working_state[6]);
        working_state[12] = AVX512_ROTL32(_mm512_xor_si512(working_state[12], working_state[1]), 16);
        working_state[11] = _mm512_add_epi32(working_state[11], working_state[12]);
        working_state[6] = AVX512_ROTL32(_mm512_xor_si512(working_state[6], working_state[11]), 12);

        working_state[1] = _mm512_add_epi32(working_state[1], working_state[6]);
        working_state[12] = AVX512_ROTL32(_mm512_xor_si512(working_state[12], working_state[1]), 8);
        working_state[11] = _mm512_add_epi32(working_state[11], working_state[12]);
        working_state[6] = AVX512_ROTL32(_mm512_xor_si512(working_state[6], working_state[11]), 7);

        // Quarter round (2, 7, 8, 13)
        working_state[2] = _mm512_add_epi32(working_state[2], working_state[7]);
        working_state[13] = AVX512_ROTL32(_mm512_xor_si512(working_state[13], working_state[2]), 16);
        working_state[8] = _mm512_add_epi32(working_state[8], working_state[13]);
        working_state[7] = AVX512_ROTL32(_mm512_xor_si512(working_state[7], working_state[8]), 12);

        working_state[2] = _mm512_add_epi32(working_state[2], working_state[7]);
        working_state[13] = AVX512_ROTL32(_mm512_xor_si512(working_state[13], working_state[2]), 8);
        working_state[8] = _mm512_add_epi32(working_state[8], working_state[13]);
        working_state[7] = AVX512_ROTL32(_mm512_xor_si512(working_state[7], working_state[8]), 7);

        // Quarter round (3, 4, 9, 14)
        working_state[3] = _mm512_add_epi32(working_state[3], working_state[4]);
        working_state[14] = AVX512_ROTL32(_mm512_xor_si512(working_state[14], working_state[3]), 16);
        working_state[9] = _mm512_add_epi32(working_state[9], working_state[14]);
        working_state[4] = AVX512_ROTL32(_mm512_xor_si512(working_state[4], working_state[9]), 12);

        working_state[3] = _mm512_add_epi32(working_state[3], working_state[4]);
        working_state[14] = AVX512_ROTL32(_mm512_xor_si512(working_state[14], working_state[3]), 8);
        working_state[9] = _mm512_add_epi32(working_state[9], working_state[14]);
        working_state[4] = AVX512_ROTL32(_mm512_xor_si512(working_state[4], working_state[9]), 7);
    }

    // 添加原始state
    __m512i original_state[16];
    for (int i = 0; i < 16; i++) {
        original_state[i] = _mm512_set_epi32(
            state[7][i], state[6][i], state[5][i], state[4][i],
            state[3][i], state[2][i], state[1][i], state[0][i],
            state[7][i], state[6][i], state[5][i], state[4][i],
            state[3][i], state[2][i], state[1][i], state[0][i]
        );
        working_state[i] = _mm512_add_epi32(working_state[i], original_state[i]);
    }

    // 将结果存储到输出数组
    for (int i = 0; i < 16; i++) {
        uint32_t temp[16];
        _mm512_storeu_si512((__m512i*)temp, working_state[i]);

        for (int j = 0; j < 8; j++) {
            out[j][i*4 + 0] = (uint8_t)(temp[j] >> 0);
            out[j][i*4 + 1] = (uint8_t)(temp[j] >> 8);
            out[j][i*4 + 2] = (uint8_t)(temp[j] >> 16);
            out[j][i*4 + 3] = (uint8_t)(temp[j] >> 24);
        }
    }
}

// 保留原始版本作为fallback
static void chacha_quarter_round(uint32_t x[16], size_t a, size_t b, size_t c, size_t d) {
    x[a] = x[a] + x[b]; x[d] = ROTL32(x[d] ^ x[a], 16);
    x[c] = x[c] + x[d]; x[b] = ROTL32(x[b] ^ x[c], 12);
    x[a] = x[a] + x[b]; x[d] = ROTL32(x[d] ^ x[a], 8);
    x[c] = x[c] + x[d]; x[b] = ROTL32(x[b] ^ x[c], 7);
}

static void chacha20_block(uint32_t state[16], uint8_t out[64]) {
    uint32_t working_state[16];
    memcpy(working_state, state, 64);

    for (int i = 0; i < 10; i++) {
        chacha_quarter_round(working_state, 0, 4, 8, 12);
        chacha_quarter_round(working_state, 1, 5, 9, 13);
        chacha_quarter_round(working_state, 2, 6, 10, 14);
        chacha_quarter_round(working_state, 3, 7, 11, 15);

        chacha_quarter_round(working_state, 0, 5, 10, 15);
        chacha_quarter_round(working_state, 1, 6, 11, 12);
        chacha_quarter_round(working_state, 2, 7, 8, 13);
        chacha_quarter_round(working_state, 3, 4, 9, 14);
    }

    for (int i = 0; i < 16; i++) {
        working_state[i] += state[i];
    }

    for (int i = 0; i < 16; i++) {
        out[i*4 + 0] = (uint8_t)(working_state[i] >> 0);
        out[i*4 + 1] = (uint8_t)(working_state[i] >> 8);
        out[i*4 + 2] = (uint8_t)(working_state[i] >> 16);
        out[i*4 + 3] = (uint8_t)(working_state[i] >> 24);
    }
}

void chacha20_encrypt(const uint8_t key[32], const uint8_t nonce[12], uint32_t initial_counter, uint8_t *buffer, size_t length) {
    uint32_t key_words[8];
    uint32_t nonce_words[3];

    for (int i = 0; i < 8; i++) {
        key_words[i] = (uint32_t)key[i*4 + 0]      |
                      ((uint32_t)key[i*4 + 1] << 8)  |
                      ((uint32_t)key[i*4 + 2] << 16) |
                      ((uint32_t)key[i*4 + 3] << 24);
    }

    for (int i = 0; i < 3; i++) {
        nonce_words[i] = (uint32_t)nonce[i*4 + 0]      |
                        ((uint32_t)nonce[i*4 + 1] << 8)  |
                        ((uint32_t)nonce[i*4 + 2] << 16) |
                        ((uint32_t)nonce[i*4 + 3] << 24);
    }

    // 计算总共有多少个64字节的块
    size_t num_blocks = (length + 63) / 64;

    // 使用AVX-512优化：每次处理8个块
    size_t avx512_blocks = num_blocks / 8;
    size_t remaining_blocks = num_blocks % 8;

    // 处理8的倍数个块，使用AVX-512并行化
    for (size_t batch_idx = 0; batch_idx < avx512_blocks; batch_idx++) {
        uint32_t states[8][16];
        uint8_t outputs[8][64];

        // 准备8个state
        for (int i = 0; i < 8; i++) {
            uint32_t current_block_counter = initial_counter + (uint32_t)(batch_idx * 8 + i);

            states[i][0] = 0x61707865; states[i][1] = 0x3320646e;
            states[i][2] = 0x79622d32; states[i][3] = 0x6b206574;
            states[i][4] = key_words[0]; states[i][5] = key_words[1];
            states[i][6] = key_words[2]; states[i][7] = key_words[3];
            states[i][8] = key_words[4]; states[i][9] = key_words[5];
            states[i][10] = key_words[6]; states[i][11] = key_words[7];
            states[i][12] = current_block_counter;
            states[i][13] = nonce_words[0]; states[i][14] = nonce_words[1];
            states[i][15] = nonce_words[2];
        }

        // 使用AVX-512并行处理8个块
        chacha20_block_avx512(states, outputs);

        // 应用密钥流到缓冲区
        for (int i = 0; i < 8; i++) {
            size_t block_idx = batch_idx * 8 + i;
            size_t current_offset = block_idx * 64;
            size_t bytes_to_process = (current_offset + 64 <= length) ? 64 : (length - current_offset);

            for (size_t j = 0; j < bytes_to_process; j++) {
                buffer[current_offset + j] ^= outputs[i][j];
            }
        }
    }

    // 处理剩余的块（少于8个），使用原始版本
    for (size_t i = 0; i < remaining_blocks; i++) {
        size_t block_idx = avx512_blocks * 8 + i;
        uint32_t current_block_counter = initial_counter + (uint32_t)block_idx;

        uint32_t state[16] = {
            0x61707865, 0x3320646e, 0x79622d32, 0x6b206574,
            key_words[0], key_words[1], key_words[2], key_words[3],
            key_words[4], key_words[5], key_words[6], key_words[7],
            current_block_counter,
            nonce_words[0], nonce_words[1], nonce_words[2]
        };

        uint8_t key_stream[64];
        chacha20_block(state, key_stream);

        size_t current_offset = block_idx * 64;
        size_t bytes_to_process = (current_offset + 64 <= length) ? 64 : (length - current_offset);

        for (size_t j = 0; j < bytes_to_process; j++) {
            buffer[current_offset + j] ^= key_stream[j];
        }
    }
}